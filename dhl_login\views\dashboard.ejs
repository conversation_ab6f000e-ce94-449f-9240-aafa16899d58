<style>
.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.dashboard-header {
  background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
  color: white;
  padding: 2rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dashboard-header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 300;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #d32f2f;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #d32f2f;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.dashboard-card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.card-icon {
  font-size: 1.5rem;
  margin-right: 0.5rem;
}

.card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.assignment-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 5px;
  padding: 1rem;
  margin-bottom: 0.5rem;
}

.assignment-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.assignment-meta {
  font-size: 0.85rem;
  color: #666;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-assigned {
  background: #fff3cd;
  color: #856404;
}

.status-inprogress {
  background: #d1ecf1;
  color: #0c5460;
}

.status-overdue {
  background: #f8d7da;
  color: #721c24;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 5px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.btn-primary {
  background: #d32f2f;
  color: white;
}

.btn-primary:hover {
  background: #b71c1c;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.btn-secondary:hover {
  background: #e0e0e0;
  transform: translateY(-1px);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>

<div class="dashboard-container">
  <header class="dashboard-header">
    <h1>Welcome, <%= user.firstName %> <%= user.lastName %>!</h1>
    <p>Your personal dashboard for sanitation checklist management</p>
  </header>

  <!-- Statistics Cards -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-number" id="activeAssignments">--</div>
      <div class="stat-label">Active Assignments</div>
    </div>
    <div class="stat-card">
      <div class="stat-number" id="overdueAssignments">--</div>
      <div class="stat-label">Overdue</div>
    </div>
    <div class="stat-card">
      <div class="stat-number" id="completedThisMonth">--</div>
      <div class="stat-label">Completed This Month</div>
    </div>
    <div class="stat-card">
      <div class="stat-number" id="totalSubmissions">--</div>
      <div class="stat-label">Total Submissions</div>
    </div>
  </div>

  <div class="dashboard-grid">
    <!-- Active Assignments -->
    <div class="dashboard-card">
      <div class="card-header">
        <span class="card-icon">📋</span>
        <h3 class="card-title">My Active Checklists</h3>
      </div>
      <div id="assignmentsContainer">
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading assignments...</p>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="dashboard-card">
      <div class="card-header">
        <span class="card-icon">🚀</span>
        <h3 class="card-title">Quick Actions</h3>
      </div>
      <div style="display: flex; flex-direction: column; gap: 0.5rem;">
        <a href="/app/index.html" class="btn btn-primary">
          <i class="fas fa-plus"></i> Start New Checklist
        </a>
        <a href="#" onclick="refreshDashboard()" class="btn btn-secondary">
          <i class="fas fa-sync"></i> Refresh Dashboard
        </a>
        <a href="/forgot-password" class="btn btn-secondary">
          <i class="fas fa-key"></i> Change Password
        </a>
      </div>
    </div>

    <!-- Recent Submissions -->
    <div class="dashboard-card">
      <div class="card-header">
        <span class="card-icon">📊</span>
        <h3 class="card-title">Recent Submissions</h3>
      </div>
      <div id="submissionsContainer">
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading submissions...</p>
        </div>
      </div>
    </div>

    <% if (user && (user.role === 'compliance' || user.role === 'manager' || user.role === 'admin' || user.isAdmin)) { %>
    <!-- Compliance Panel -->
    <div class="dashboard-card">
      <div class="card-header">
        <span class="card-icon">🛡️</span>
        <h3 class="card-title">Compliance Panel</h3>
      </div>
      <div style="display: flex; flex-direction: column; gap: 0.5rem;">
        <a href="/compliance" class="btn btn-secondary">
          <i class="fas fa-shield-alt"></i> Compliance Dashboard
        </a>
        <a href="/compliance/metrics" class="btn btn-secondary">
          <i class="fas fa-chart-bar"></i> Compliance Metrics
        </a>
        <a href="/compliance/audit" class="btn btn-secondary">
          <i class="fas fa-search"></i> Audit Trail
        </a>
        <a href="/compliance/non-compliance" class="btn btn-secondary">
          <i class="fas fa-exclamation-triangle"></i> Non-Compliance Reports
        </a>
      </div>
    </div>
    <% } %>

    <% if (user && (user.role === 'manager' || user.role === 'admin' || user.isAdmin)) { %>
    <!-- Manager Panel -->
    <div class="dashboard-card">
      <div class="card-header">
        <span class="card-icon">👥</span>
        <h3 class="card-title">Manager Panel</h3>
      </div>
      <div style="display: flex; flex-direction: column; gap: 0.5rem;">
        <a href="/manager" class="btn btn-secondary">
          <i class="fas fa-users"></i> Manager Dashboard
        </a>
        <a href="/manager/teams" class="btn btn-secondary">
          <i class="fas fa-users-cog"></i> Team Management
        </a>
        <a href="/manager/performance" class="btn btn-secondary">
          <i class="fas fa-chart-line"></i> Performance Analytics
        </a>
        <a href="/manager/assignments" class="btn btn-secondary">
          <i class="fas fa-tasks"></i> Manual Assignments
        </a>
      </div>
    </div>
    <% } %>

    <% if (user && (user.role === 'admin' || user.isAdmin)) { %>
    <!-- Admin Panel -->
    <div class="dashboard-card">
      <div class="card-header">
        <span class="card-icon">⚙️</span>
        <h3 class="card-title">Admin Panel</h3>
      </div>
      <div style="display: flex; flex-direction: column; gap: 0.5rem;">
        <a href="/admin" class="btn btn-secondary">
          <i class="fas fa-tachometer-alt"></i> Admin Dashboard
        </a>
        <a href="/admin/postgresql" class="btn btn-secondary">
          <i class="fas fa-database"></i> PostgreSQL Dashboard
        </a>
        <a href="/admin/automation-rules" class="btn btn-secondary">
          <i class="fas fa-robot"></i> Automation Rules
        </a>
        <a href="/admin/users/new" class="btn btn-secondary">
          <i class="fas fa-user-plus"></i> Create User
        </a>
      </div>
    </div>
    <% } %>
  </div>
</div>

<script>
// Dashboard functionality
let backendApiUrl = 'http://localhost:3001'; // Default backend URL

document.addEventListener('DOMContentLoaded', async function() {
    // Clear any existing tokens that might be invalid
    localStorage.removeItem('jwtToken');
    sessionStorage.removeItem('jwtToken');
    
    console.log('Cleared existing tokens, requesting fresh authentication');
    
    // Get a fresh token
    const authenticated = await ensureAuthentication();
    if (authenticated) {
        // Load data with delays to prevent rate limiting
        loadUserStats();
        setTimeout(() => loadActiveAssignments(), 500);
        setTimeout(() => loadRecentSubmissions(), 1000);
    } else {
        console.warn('User not authenticated for API calls');
        // Redirect to login page if authentication fails
        window.location.href = '/login-page';
    }
}); // Add this at the top of your script section, right after the backendApiUrl declaration

// Get JWT token from localStorage or session
function getJWTToken() {
    const token = localStorage.getItem('jwtToken') || sessionStorage.getItem('jwtToken');
    console.log('JWT Token found:', token ? 'Yes (length: ' + token.length + ')' : 'No');
    return token;
}

// Add this function to decode and check the token
function decodeJWT(token) {
    try {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));
        
        return JSON.parse(jsonPayload);
    } catch (e) {
        console.error('Error decoding JWT:', e);
        return null;
    }
}

// Make authenticated API request
async function makeAuthenticatedRequest(url, options = {}) {
    const token = getJWTToken();
    if (!token) {
        console.error('No JWT token found');
        return null;
    }

    console.log('[makeAuthenticatedRequest] Using token:', token.substring(0, 50) + '...');
    console.log('[makeAuthenticatedRequest] Making request to:', url);

    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers
    };

    try {
        const response = await fetch(url, {
            ...options,
            headers
        });

        console.log('[makeAuthenticatedRequest] Response status:', response.status);

        if (!response.ok) {
            if (response.status === 401) {
                console.error('[makeAuthenticatedRequest] 401 Unauthorized - token may be expired or invalid');
                // Try to get a fresh token (but only retry once to prevent loops)
                if (!options._retryAttempted) {
                    const newToken = await ensureAuthentication();
                    if (newToken) {
                        console.log('[makeAuthenticatedRequest] Got fresh token, retrying request...');
                        // Mark this as a retry attempt to prevent infinite loops
                        const retryOptions = { ...options, _retryAttempted: true };
                        return makeAuthenticatedRequest(url, retryOptions);
                    }
                }
            } else if (response.status === 429) {
                console.error('[makeAuthenticatedRequest] 429 Rate Limited - too many requests');
                // Don't retry on rate limit errors
                return { success: false, error: 'Rate limited', status: 429 };
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API request failed:', error);
        return null;
    }
}

// Load user statistics
async function loadUserStats() {
    const data = await makeAuthenticatedRequest(`${backendApiUrl}/api/user/stats`);

    if (data && data.success) {
        document.getElementById('activeAssignments').textContent = data.stats.activeAssignments;
        document.getElementById('overdueAssignments').textContent = data.stats.overdueAssignments;
        document.getElementById('completedThisMonth').textContent = data.stats.completedThisMonth;
        document.getElementById('totalSubmissions').textContent = data.stats.totalSubmissions;
    } else {
        // Show placeholder values on error
        document.getElementById('activeAssignments').textContent = '--';
        document.getElementById('overdueAssignments').textContent = '--';
        document.getElementById('completedThisMonth').textContent = '--';
        document.getElementById('totalSubmissions').textContent = '--';
    }
}

// Load active assignments
async function loadActiveAssignments() {
    const container = document.getElementById('assignmentsContainer');
    const data = await makeAuthenticatedRequest(`${backendApiUrl}/api/user/assignments`);

    if (data && data.success) {
        if (data.assignments.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-clipboard-check"></i>
                    <p>No active assignments</p>
                    <small>New assignments will appear here when created by automation rules.</small>
                </div>
            `;
        } else {
            container.innerHTML = data.assignments.map(assignment => {
                const dueDate = assignment.due_timestamp ? new Date(assignment.due_timestamp) : null;
                const isOverdue = dueDate && dueDate < new Date();
                const statusClass = isOverdue ? 'status-overdue' :
                                  assignment.assignment_status === 'InProgress' ? 'status-inprogress' : 'status-assigned';

                return `
                    <div class="assignment-item">
                        <div class="assignment-title">${assignment.checklist_title}</div>
                        <div class="assignment-meta">
                            <span>
                                ${dueDate ? `Due: ${dueDate.toLocaleDateString()}` : 'No due date'}
                            </span>
                            <span class="status-badge ${statusClass}">
                                ${isOverdue ? 'Overdue' : assignment.assignment_status}
                            </span>
                        </div>
                        <div style="margin-top: 0.5rem;">
                            <button class="btn btn-primary btn-sm start-assignment-btn" data-assignment-id="${assignment.assignment_id}">
                                <i class="fas fa-play"></i> Start
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }
    } else {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <p>Failed to load assignments</p>
                <button class="btn btn-secondary btn-sm retry-assignments-btn">
                    <i class="fas fa-retry"></i> Retry
                </button>
            </div>
        `;
    }
}

// Load recent submissions
async function loadRecentSubmissions() {
    const container = document.getElementById('submissionsContainer');
    console.log('Fetching submissions from:', `${backendApiUrl}/api/user/submissions?limit=5`);
    
    try {
        const data = await makeAuthenticatedRequest(`${backendApiUrl}/api/user/submissions?limit=5`);
        console.log('Submissions API response:', data);
        
        if (data && data.success) {
            if (data.submissions.length === 0) {
                console.log('No submissions found for user');
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-file-alt"></i>
                        <p>No submissions yet</p>
                        <small>Your completed checklists will appear here.</small>
                    </div>
                `;
            } else {
                console.log(`Found ${data.submissions.length} submissions`);
                container.innerHTML = data.submissions.map(submission => {
                    const submissionDate = new Date(submission.submission_timestamp);
                    const validationStatus = submission.validation_timestamp ? 'Validated' : 'Pending Validation';

                    return `
                        <div class="assignment-item">
                            <div class="assignment-title">${submission.checklist_title}</div>
                            <div class="assignment-meta">
                                <span>Submitted: ${submissionDate.toLocaleDateString()}</span>
                                <span class="status-badge ${submission.validation_timestamp ? 'status-inprogress' : 'status-assigned'}">
                                    ${validationStatus}
                                </span>
                            </div>
                        </div>
                    `;
                }).join('');
            }
        } else {
            console.error('Failed to load submissions:', data);
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Failed to load submissions</p>
                    <button class="btn btn-secondary btn-sm retry-submissions-btn">
                        <i class="fas fa-retry"></i> Retry
                    </button>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error in loadRecentSubmissions:', error);
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <p>Error: ${error.message}</p>
                <button class="btn btn-secondary btn-sm retry-submissions-btn">
                    <i class="fas fa-retry"></i> Retry
                </button>
            </div>
        `;
    }
}

// Start an assignment
async function startAssignment(assignmentId) {
    // Update assignment status to InProgress
    const data = await makeAuthenticatedRequest(`${backendApiUrl}/api/assignments/${assignmentId}/status`, {
        method: 'PATCH',
        body: JSON.stringify({ status: 'InProgress' })
    });

    if (data && data.success) {
        // Redirect to the checklist application
        window.location.href = '/app/index.html';
    } else {
        alert('Failed to start assignment. Please try again.');
    }
}

// Refresh dashboard data
function refreshDashboard() {
    loadUserStats();
    setTimeout(() => loadActiveAssignments(), 500);
    setTimeout(() => loadRecentSubmissions(), 1000);
}

// Check if user has JWT token, if not, try to get one
async function ensureAuthentication() {
    const token = getJWTToken();
    console.log('[ensureAuthentication] Current token:', token ? 'exists' : 'not found');

    if (token) {
        // Decode and check the token
        const decoded = decodeJWT(token);
        console.log('[ensureAuthentication] Decoded token:', decoded);
        
        // Check if token has required fields
        if (!decoded || !decoded.userId) {
            console.error('[ensureAuthentication] Token missing required fields, clearing it');
            localStorage.removeItem('jwtToken');
            sessionStorage.removeItem('jwtToken');
            // Continue to get a new token
        } else {
            console.log('[ensureAuthentication] Using existing token');
            return true;
        }
    }
    
    if (!token) {
        console.log('[ensureAuthentication] No token found, requesting new one...');
        // Try to get a new token using session authentication
        try {
            const response = await fetch('/api/auth/token', {
                method: 'GET',  // Changed from POST to GET to match compliance dashboard
                credentials: 'include'
            });

            console.log('[ensureAuthentication] Token request response status:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('[ensureAuthentication] Token response data:', data);
                if (data.token) {
                    localStorage.setItem('jwtToken', data.token);
                    console.log('[ensureAuthentication] Token saved to localStorage');
                    return true;
                }
            } else {
                console.error('[ensureAuthentication] Token request failed with status:', response.status);
            }
        } catch (error) {
            console.error('[ensureAuthentication] Failed to get JWT token:', error);
        }
        return false;
    }
    console.log('[ensureAuthentication] Using existing token');
    return true;
}


</script>
